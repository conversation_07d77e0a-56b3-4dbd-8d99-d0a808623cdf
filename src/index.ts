import { Hono, Next } from 'hono';
import authRoute from './feature/auth/auth';
import { cors } from 'hono/cors';
import partnerRoute from './feature/partner/partner';
import shuttleRoute from './feature/shuttle/shuttle';
import routeRoute from './feature/route/route';
import scheduleRoute from './feature/schedule/schedule';
import paymentRoute from './feature/payment/payment';
import bookingRoute from './feature/booking/booking';
import earningsRoute from './feature/earnings/earnings';
import organizationRoute from './feature/organization/organization';

const app = new Hono();

app.use('*', cors({
    origin: '*',  // Allows requests from any origin
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowHeaders: ['Authorization', 'Content-Type'],
  }));

// STATUS CHECK
app.get('/status', (c) => {
    return c.json({ status: 'Server is running' });
});

// ROUTES
app.route('/api/v1/auth', authRoute);
app.route('/api/v1/partners', partnerRoute);
app.route('/api/v1/shuttles', shuttleRoute);
app.route('/api/v1/routes', routeRoute);
app.route('/api/v1/schedules', scheduleRoute);
app.route('/api/v1/payment', paymentRoute);
app.route('/api/v1/bookings', bookingRoute);
app.route('/api/v1/earnings', earningsRoute);
app.route('/api/v1/organization', organizationRoute);

export default app;
