import { Next } from "hono/types";
import { ForbiddenException } from "../exception";

// Role-Based Access Middleware
export function roleGuard(allowedRoles: string[]) {
    return async (c: any, next: Next) => {
        const user = c.get('user');
        
        // Ensure that the user exists and has a valid role
        if (!user || !user.role || !allowedRoles.includes(user.role)) {
            // Generic error message, avoid over-revealing internal details
            throw new ForbiddenException('You do not have the required permissions');
        }

        // Proceed to the next middleware or route handler
        await next();
    };
}