export interface ILocation {
    name: string;
    address: string;
    lat: number;
    lng: number;
}

export interface IBooking {
    id: string;
    status: string;
    rider: {
        id: string;
        first_name: string;
        last_name: string;
        avatar_url?: string;
    };
    partner_avatar_url?: string;
    number_of_tickets: number;
    total_amount: number;
    booking_reference: string;
    arrival_time: string;
    departure_time: string;
    route: {
        origin: ILocation;
        destination: ILocation;
    };
    pickup_stop: ILocation;
    drop_off_stop: ILocation;
    booking_schedule_date: string;
}

export interface Schedule {
    id: string;
    departure_time: string;
    status: string;
    booked_seats?: number;
    route?: {
        origin: string;
        destination: string;
        base_price: number;
    };
    shuttle?: {
        name: string;
        capacity: number;
    };
}

export interface Customer {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_number: string;
    avatar_url?: string;
    created_at: string;
}

export interface DashboardStats {
    totalRevenue: number;
    completedBookings: number;
    activeRoutes: number;
    todayBookings: number;
}

export interface PerformanceTrend {
    trend: Array<{
        x: Date;
        y: number;
    }>;
    stats: {
        total: number;
        average: number;
        peak: number;
        growth: number;
    };
}

export interface OrganizationFilter {
    start_date?: string;
    end_date?: string;
    partner_id?: string;
    route_id?: string;
}
