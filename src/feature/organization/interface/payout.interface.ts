export interface PayoutSummary {
    total_payouts_this_month: number;
    growth_percentage: number;
    completed: {
        count: number;
        amount: number;
    };
    pending: {
        count: number;
        amount: number;
    };
    scheduled: {
        count: number;
        amount: number;
        next_payout_days: number;
    };
    average_payout: number;
    total_payouts_count: number;
}

export interface PayoutDetail {
    id: string;
    partner_id: string;
    amount: number;
    currency: string;
    status: 'completed' | 'pending' | 'scheduled' | 'failed';
    payout_date: string;
    created_at: string;
    updated_at: string;
    reference: string;
    description?: string;
    payment_method?: string;
    transaction_id?: string;
}

export interface PayoutFilter {
    start_date?: string;
    end_date?: string;
    status?: 'completed' | 'pending' | 'scheduled' | 'failed';
    partner_id?: string;
    page?: number;
    limit?: number;
}

export interface PayoutListResponse {
    data: PayoutDetail[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        total_pages: number;
    };
    summary: {
        total_amount: number;
        count: number;
    };
}
