import { SupabaseClient } from '@supabase/supabase-js';
import { 
    PayoutSummary, 
    PayoutDetail, 
    PayoutFilter, 
    PayoutListResponse 
} from '../interface/payout.interface';
import { BadRequestException } from '../../../core/exception';

export class PayoutService {
    constructor(private readonly supabase: SupabaseClient) {}

    async getPayoutSummary(partnerId?: string): Promise<PayoutSummary> {
        try {
            // For now, return mock data that matches the UI
            // TODO: Replace with actual database queries when payouts table is ready

            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 500));

            return {
                total_payouts_this_month: 150000.00,
                growth_percentage: 12.5,
                completed: {
                    count: 8,
                    amount: 95000.00
                },
                pending: {
                    count: 3,
                    amount: 30000.00
                },
                scheduled: {
                    count: 1,
                    amount: 45000.00,
                    next_payout_days: 7
                },
                average_payout: 11363.64,
                total_payouts_count: 11
            };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to retrieve payout summary');
        }
    }

    async getPayoutDetails(filters: PayoutFilter): Promise<PayoutListResponse> {
        try {
            const page = filters.page || 1;
            const limit = filters.limit || 10;

            // Simulate async operation
            await new Promise(resolve => setTimeout(resolve, 500));

            // Mock payout data
            const mockPayouts: PayoutDetail[] = [
                {
                    id: 'payout-1',
                    partner_id: 'partner-1',
                    amount: 15000.00,
                    currency: 'GHS',
                    status: 'completed',
                    payout_date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
                    updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
                    reference: 'PAY-001',
                    description: 'Weekly payout for completed rides',
                    payment_method: 'Bank Transfer',
                    transaction_id: 'TXN-12345'
                },
                {
                    id: 'payout-2',
                    partner_id: 'partner-1',
                    amount: 8500.00,
                    currency: 'GHS',
                    status: 'pending',
                    payout_date: new Date().toISOString(),
                    created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
                    reference: 'PAY-002',
                    description: 'Pending payout for recent bookings',
                    payment_method: 'Mobile Money'
                },
                {
                    id: 'payout-3',
                    partner_id: 'partner-1',
                    amount: 22000.00,
                    currency: 'GHS',
                    status: 'scheduled',
                    payout_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    reference: 'PAY-003',
                    description: 'Scheduled payout for next week',
                    payment_method: 'Bank Transfer'
                }
            ];

            // Apply status filter if provided
            let filteredPayouts = mockPayouts;
            if (filters.status) {
                filteredPayouts = mockPayouts.filter(p => p.status === filters.status);
            }

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPayouts = filteredPayouts.slice(startIndex, endIndex);

            const totalAmount = paginatedPayouts.reduce((sum, payout) => sum + payout.amount, 0);
            const totalPages = Math.ceil(filteredPayouts.length / limit);

            return {
                data: paginatedPayouts,
                pagination: {
                    page,
                    limit,
                    total: filteredPayouts.length,
                    total_pages: totalPages
                },
                summary: {
                    total_amount: totalAmount,
                    count: paginatedPayouts.length
                }
            };
        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Failed to retrieve payout details');
        }
    }
}
