import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import authMiddleware from '../auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { PayoutService } from './service/payout.service';
import { OrganizationService } from './service/organization.service';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const organizationRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
organizationRoute.use('*', authMiddleware);

// Supabase middleware
organizationRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Get all stats for the dashboard
organizationRoute.get('/stats', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        const stats = await organizationService.getDashboardStats(filters);
        return c.json(stats);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

//Get business earnings performance trend
organizationRoute.get('/performance/earnings/trend', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        const trend = await organizationService.getEarningsTrend(filters);
        return c.json(trend);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

//Get business bookings performance trend
organizationRoute.get('/performance/bookings/trend', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        const trend = await organizationService.getBookingsTrend(filters);
        return c.json(trend);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

// Get the summary of recent bookings
organizationRoute.get('/bookings/recent', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };

    try {
        const bookings = await organizationService.getRecentBookings(filters);
        return c.json(bookings);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

// Get today's schedules
organizationRoute.get('/schedules/today', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        route_id: c.req.query('route_id'),
    };

    try {
        const schedules = await organizationService.getTodaySchedules(filters);
        return c.json(schedules);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

// Get recent customers
organizationRoute.get('/customers/recent', async (c: Context) => {
    const organizationService = new OrganizationService(c.get('supabase'));

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
    };

    try {
        const customers = await organizationService.getRecentCustomers(filters);
        return c.json(customers);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

// Get payouts summary
organizationRoute.get('/payouts/summary', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const payoutService = new PayoutService(c.get('supabase'));
    const user = c.get('user');

    try {
        // If user is a partner, restrict to their own data
        const partnerId = user.role === Role.USER.toString() ? user.orgId : undefined;

        const summary = await payoutService.getPayoutSummary(partnerId);

        return c.json({
            status: 'success',
            data: summary
        });
    } catch (error: any) {
        return c.json({
            status: 'error',
            message: error.message || 'Error getting payout summary'
        }, 400);
    }
});

// Get payout details with pagination and filtering
organizationRoute.get('/payouts', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const payoutService = new PayoutService(c.get('supabase'));
    const user = c.get('user');

    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        status: c.req.query('status') as 'completed' | 'pending' | 'scheduled' | 'failed' | undefined,
        page: c.req.query('page') ? parseInt(c.req.query('page')!) : undefined,
        limit: c.req.query('limit') ? parseInt(c.req.query('limit')!) : undefined,
        partner_id: user.role === Role.USER.toString() ? user.orgId : c.req.query('partner_id')
    };

    try {
        const payouts = await payoutService.getPayoutDetails(filters);

        return c.json({
            status: 'success',
            data: payouts
        });
    } catch (error: any) {
        return c.json({
            status: 'error',
            message: error.message || 'Error getting payout details'
        }, 400);
    }
});

export default organizationRoute;
