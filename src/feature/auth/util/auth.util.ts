import { Context } from 'hono';
import { sign } from 'hono/jwt';

// Helper function to generate JWT
export const generateTokens = async (c: Context, userId: string,email:string, role: string) => {

  const accessToken = await sign({
    sub: userId,
    role: role,
    email: email,
    type:'access',
    exp: Math.floor(Date.now() / 1000) + 1800 // 1 hour expiry
  }, c.env.JWT_SECRET);

  const refreshToken = await sign({
    sub: userId,
    role: role,
    email: email,
    type:'refresh',
    exp: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days expiry
  }, c.env.JWT_REFRESH_SECRET); // Use a different secret for refresh tokens

  return { accessToken, refreshToken };
  }