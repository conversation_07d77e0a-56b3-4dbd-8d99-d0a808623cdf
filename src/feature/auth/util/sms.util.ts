import { Bindings } from "../../../core/type/bindings";

interface SmsDTO {
    from?: string;
    to: string;
    message: string;
}
export async function sendSMS(options: SmsDTO, ctx: { env: Bindings }) {
    try {
        if (!ctx.env.SMSC_BASE_URL) {
            throw new Error('SMSC_BASE_URL environment variable is not set. Please add it to .dev.vars for local development or configure it in your Cloudflare Worker settings for production.');
        }

        if (!ctx.env.SMSC_CLIENT_ID) {
            throw new Error('SMSC_CLIENT_ID environment variable is not set. Please add it to .dev.vars for local development or configure it in your Cloudflare Worker settings for production.');
        }

        if (!ctx.env.SMSC_CLIENT_SECRET) {
            throw new Error('SMSC_CLIENT_SECRET environment variable is not set. Please add it to .dev.vars for local development or configure it in your Cloudflare Worker settings for production.');
        }

        console.log("Sending SMS");

        // Construct URL with query parameters
        const url = new URL(ctx.env.SMSC_BASE_URL);
        url.searchParams.append('clientid', ctx.env.SMSC_CLIENT_ID);
        url.searchParams.append('clientsecret', ctx.env.SMSC_CLIENT_SECRET);
        url.searchParams.append('from', options.from || 'Kwanso');
        url.searchParams.append('to', options.to);
        url.searchParams.append('content', options.message);

        const response = await fetch(url.toString(), {
            method: 'GET',
        });

        if (!response.ok) {
            const errorData = await response.json() as { message?: string, error?: { message: string } };
            throw new Error(errorData.error?.message || errorData.message || 'Failed to send sms');
        }

        const smsData = await response.json() as any;
        console.log("Successfully sent sms:", smsData);

        return {
            ...smsData,
        };
    } catch (error) {
        console.error('Failed to send sms:', error);
       throw error
    }
}