import { Bindings } from '../../../core/type/bindings';

interface ResendEmailResponse {
  id: string;
  from: string;
  to: string[];
  created_at: string;
}

interface EmailOptions {
  to: string[];
  subject: string;
  html: string;
  from?: string;
  replyTo?: string;
}

/**
 * Sends an email using the Mailgun API
 * @param options EmailOptions containing recipient, subject, and content details
 * @param ctx Hono context containing environment bindings
 * @returns Promise resolving to the sent email data or error
 */
export async function sendEmail(options: EmailOptions, ctx: { env: Bindings }) {
  try {
    if (!ctx.env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY environment variable is not set. Please add it to .dev.vars for local development or configure it in your Cloudflare Worker settings for production.');
    }

    console.log("Sending email");
    const defaultFrom = 'Kwanso <<EMAIL>>';

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${ctx.env.RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: options.from || defaultFrom,
        to: options.to,
        subject: options.subject,
        html: options.html,
        reply_to: options.replyTo,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json() as { message?: string, error?: { message: string } };
      throw new Error(errorData.error?.message || errorData.message || 'Failed to send email');
    }

    const emailData = await response.json() as ResendEmailResponse;
    console.log("Successfully sent email:", emailData);

    return {
      success: true,
      data: emailData,
    };
  } catch (error) {
    console.error('Failed to send email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send email',
    };
  }
}

/**
 * Sends a welcome email to a new user
 * @param email User's email address
 * @param name User's full name
 * @param ctx Hono context containing environment bindings
 */
export async function sendWelcomeEmail(email: string, name: string, ctx: { env: Bindings }) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #6941C6;">Welcome to CFC Screening Tool!</h1>
      <p>Dear ${name},</p>
      <p>Welcome to the CFC Screening Tool! We're excited to have you on board.</p>
      <p>You can now start using the tool to:</p>
      <ul>
        <li>Screen children for various developmental needs</li>
        <li>Track progress over time</li>
        <li>Generate comprehensive reports</li>
      </ul>
      <p>If you have any questions, feel free to reply to this email.</p>
      <p>Best regards,<br>The CFC Team</p>
    </div>
  `;

  return await sendEmail({
    to: [email],
    subject: 'Welcome to CFC Screening Tool',
    html,
  }, ctx);
}

/**
 * Sends a password reset email
 * @param email User's email address
 * @param resetToken Password reset token
 * @param resetUrl Base URL for password reset
 * @param ctx Hono context containing environment bindings
 */
/**
 * Sends an email verification code to a new user
 * @param email User's email address
 * @param name User's full name
 * @param verificationCode The verification code to be sent
 * @param ctx Hono context containing environment bindings
 */
export async function sendVerificationEmail(
  email: string,
  name: string,
  verificationCode: string,
  ctx: { env: Bindings }
) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #6941C6;">Verify Your Email</h1>
      <p>Dear ${name},</p>
      <p>Thank you for signing up for the CFC Screening Tool. To complete your registration, please enter the verification code below:</p>
      <div style="
        background-color: #F9F5FF;
        border: 1px solid #E9D7FE;
        border-radius: 8px;
        padding: 16px;
        margin: 24px 0;
        text-align: center;
      ">
        <p style="
          font-size: 32px;
          font-weight: bold;
          color: #6941C6;
          letter-spacing: 4px;
          margin: 0;
        ">${verificationCode}</p>
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you didn't create an account with us, you can safely ignore this email.</p>
      <p>Best regards,<br>The CFC Team</p>
    </div>
  `;

  return sendEmail({
    to: [email],
    subject: 'Verify Your Email - CFC Screening Tool',
    html,
  }, ctx);
}

export async function sendConfirmationEmail(
  email: string,
  name: string,
  confirmationLink: string,
  ctx: { env: Bindings }
) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #6941C6;">Confirm Your Email</h1>
      <p>Dear ${name},</p>
      <p>Thank you for signing up for the CFC Screening Tool. To complete your registration, please confirm your email address by clicking the button below:</p>
      <div style="text-align: center; margin: 24px 0;">
        <a href="${confirmationLink}" style="
          display: inline-block;
          background-color: #6941C6;
          color: #ffffff;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 16px;
          font-weight: bold;
        ">Confirm Email</a>
      </div>
      <p>This link will expire in 15 minutes.</p>
      <p>If you didn't create an account with us, you can safely ignore this email.</p>
      <p>Best regards,<br>The CFC Team</p>
    </div>
  `;

  return sendEmail(
    {
      to: [email],
      subject: 'Confirm Your Email - CFC Screening Tool',
      html,
    },
    ctx
  );
}


export async function sendPasswordResetWithCodeEmail(
  email: string,
  name: string,
  verificationCode: string,
  ctx: { env: Bindings }
) {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #6941C6;">Reset Your Password</h1>
      <p>Dear ${name},</p>
      <p>We received a request to reset your password for the CFC Screening Tool. Please use the verification code below to reset your password:</p>
      <div style="
        background-color: #F9F5FF;
        border: 1px solid #E9D7FE;
        border-radius: 8px;
        padding: 16px;
        margin: 24px 0;
        text-align: center;
      ">
        <p style="
          font-size: 32px;
          font-weight: bold;
          color: #6941C6;
          letter-spacing: 4px;
          margin: 0;
        ">${verificationCode}</p>
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you did not request a password reset, please ignore this email.</p>
      <p>Best regards,<br>The CFC Team</p>
    </div>
  `;

  return sendEmail({
    to: [email],
    subject: 'Password Reset - CFC Screening Tool',
    html,
  }, ctx);
}


export async function sendPasswordResetWithLinkEmail(
  email: string,
  resetToken: string,
  resetUrl: string,
  ctx: { env: Bindings }
) {
  const resetLink = `${resetUrl}?token=${resetToken}`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #6941C6;">Reset Your Password</h1>
      <p>You requested to reset your password for the CFC Screening Tool.</p>
      <p>Click the button below to reset your password. This link will expire in 1 hour.</p>
      <a href="${resetLink}" style="
        display: inline-block;
        background-color: #6941C6;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 4px;
        margin: 16px 0;
      ">Reset Password</a>
      <p>If you didn't request this, you can safely ignore this email.</p>
      <p>Best regards,<br>The CFC Team</p>
    </div>
  `;

  return await sendEmail({
    to: [email],
    subject: 'Reset Your CFC Screening Tool Password',
    html,
  }, ctx);
}


export async function sendMigrationEmail(
  email: string,
  name: string,
  ctx: { env: Bindings }
) {
  // Construct the confirmation link.
  const confirmationLink = `https://disability-detect.cfc-api.uk/api/v1/auth/email-verified?email=${encodeURIComponent(email)}`;

  const html = `
   <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; text-align: center; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
  <p>Hello ${name},</p>
  <p>We are upgrading our platform to provide you with a better and more secure experience. As part of this transition, we need you to confirm your email address to complete the migration of your account.</p>
  <p>To proceed, please click the button below to confirm your email:</p>
  <div style="text-align: center; margin: 24px 0;">
    <a href="${confirmationLink}" style="
      display: inline-block;
      background-color: #6941C6;
      color: #ffffff;
      text-decoration: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: bold;
    ">Confirm Email</a>
  </div>
  <p><strong>Next Steps:</strong></p>
  <ul style="text-align: left; margin: 0 auto; display: inline-block;">
    <li>Once confirmed, visit the new platform.</li>
    <li>Use the “Forgot Password” option to reset your password.</li>
    <li>Create a new password and sign in to access your account.</li>
  </ul>
  <p style="margin-top: 20px;">This confirmation link will expire in 15 minutes.</p>
  <p>If you have any questions or need assistance, please contact our support team.</p>
  <p>Best regards,<br><strong>The CFC Team</strong></p>
</div>

  `;

  return sendEmail(
    {
      to: [email],
      subject: 'Action Required: Confirm Your Email for Migration',
      html,
    },
    ctx
  );
}
