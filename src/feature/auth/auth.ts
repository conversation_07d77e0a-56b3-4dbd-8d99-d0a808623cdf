import { Context, Hono, Next } from 'hono'
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import { Bindings } from '../../core/type/bindings'
import { HTTPException } from 'hono/http-exception'
import { generateTokens } from './util/auth.util'
import authMiddleware from './middleware/auth.middleware'
import { BadRequestException } from '../../core/exception'
import { sendVerificationEmail } from './util/email.util'
import { customAlphabet, nanoid } from 'nanoid';
import refreshTokenMiddleware from './middleware/refreshToken.middleware'
import { sendSMS } from './util/sms.util'


// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const authRoute = new Hono<{ Bindings: Bindings }>()

// Supabase middleware
authRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

//TEST NUMBERS
//DRIVER & RIDER
const testNumbers = ['*********', '*********']
const testCountryCode = '+233'
const testCodes = ['654321', '444555']


// Account creation endpoint
authRoute.post('/signup', async (c) => {
    // Parse JSON body for email, password, and any additional details
    const { user, password } = await c.req.json();

    // Validate required fields
    if (!user) {
        throw new BadRequestException('User data is required');
    }
    if (!password) {
        throw new BadRequestException('Password is required');
    }
    if (!user.phone_number) {
        throw new BadRequestException('Phone number is required');
    }
    if (!user.email) {
        throw new BadRequestException('Email is required');
    }
    if (!user.first_name) {
        throw new BadRequestException('First name is required');
    }
    if (!user.last_name) {
        throw new BadRequestException('Last name is required');
    }
    if (!user.role.id) {
        throw new BadRequestException('Role id is required');
    }


    const supabase = c.get('supabase')

    // Generate a unique 6-character code using nanoid
    const verificationCode = nanoid(6);
    const codeCreatedAt = new Date().toISOString();

    // 1. Create the user in the auth system
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        phone: `${user.phone_number}`,
        phone_confirm: true,
        email_confirm: true,
        email: user.email,
        password,
    });

    if (authError || !authData.user) {
        throw new HTTPException(500, { message: authError?.message || 'User creation failed' });
    }

    const userId = authData.user.id;

    // 2. Insert additional user details into the `users` table.
    // Adjust the table name and fields as per your schema.
    const { data: userData, error: userError } = await supabase
        .from('users')
        .insert([{
            id: userId,
            first_name: user.first_name,
            last_name: user.last_name,
            email: user.email,
            phone_number: user.phone_number,
            country_code: user.country_code,
            // organization_id: user.org.id,
            role_id: user.role.id,
            // country: user.country.id,
            code: verificationCode,
            code_created_at: codeCreatedAt,
            avatar_url: `https://api.dicebear.com/9.x/thumbs/png?seed=${user.first_name}_${user.last_name}`
        }]);

    if (userError) {
        // Optionally, consider deleting th auth user to avoid orphaned records.
        throw new HTTPException(500, { message: userError.message || 'Failed to create user details' });
    }

    // Send the verification code via email
    // await sendVerificationEmail(user.email, user.first_name, verificationCode, c)

    // Generate tokens for the newly created user
    const token = await generateTokens(
        c,
        userId,
        user.email,
        user.role.id,
    );

    return c.json({
        id: userId,
        access_token: token.accessToken,
        refresh_token: token.refreshToken,
        token_type: 'Bearer',
        expires_in: 3600
    });
});

// Sign-in route using Supabase
authRoute.post('/login', async (c) => {
    const { email, password } = await c.req.json()

    if (!email) {
        throw new BadRequestException('Email is required')
    }

    if (!password) {
        throw new BadRequestException('Password is required')
    }

    const supabase = c.get('supabase')

    const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
    })

    if (error || !data.user) {
        throw new HTTPException(401, { message: error?.message || 'Login failed' })
    }

    const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, role_id')
        .eq('id', data.user.id)
        .single();

    if (error || !user) {
        console.error("❌ User not found or error:", userError?.message);
        throw new HTTPException(404, { message: 'User not found' });
    }

    // Generate custom JWT with role
    const token = await generateTokens(c, data.user.id, email, user.role_id ?? '')

    return c.json({
        access_token: token.accessToken,
        refresh_token: token.refreshToken,
        token_type: 'Bearer',
        expires_in: 3600
    })
})

// Initiate phone authentication
authRoute.post('/phone/init', async (c) => {
    try {
        const { phone, country_code } = await c.req.json();
        const cleanedPhoneNumber = phone.replace(/[^0-9]/g, '');

        if (!phone) {
            throw new BadRequestException('Phone number is required');
        }
        if (!country_code) {
            throw new BadRequestException('Country code is required');
        }



        //FOR TEST NUMBERS
        if (country_code === testCountryCode && testNumbers.includes(cleanedPhoneNumber)) {
            console.log("Test number detected");
            return c.json({ message: 'Verification code sent' });
        }

        const nanoidNumbers = customAlphabet('0123456789', 6);
        const verificationCode = nanoidNumbers();
        const codeCreatedAt = new Date().toISOString();
        const supabase = c.get('supabase');

        // Store OTP temporarily
        const { error } = await supabase
            .from('verification_codes')
            .insert([{
                phone : cleanedPhoneNumber,
                code: verificationCode,
                country_code,
                created_at: codeCreatedAt,
                expires_at: new Date(Date.now() + 10 * 60000).toISOString() // 10 minutes expiry
            }]);

        if (error) {
            console.log(error)
            throw new HTTPException(500, { message: 'Failed to initiate verification' });
        }

        // Send OTP via SMS
        await sendSMS({
            message: `Your verification code is: ${verificationCode}`,
            to: `${country_code}${cleanedPhoneNumber}`,
        }, c);

        return c.json({ message: 'Verification code sent' });
    } catch (error) {
        console.error('Phone verification initialization error:', error);

        if (error instanceof BadRequestException || error instanceof HTTPException) {
            throw error;
        }

        throw new HTTPException(500, {
            message: error instanceof Error ? error.message : 'Failed to process verification request'
        });
    }
});

// Verify OTP
authRoute.post('/phone/verify', async (c) => {
    const supabase = c.get('supabase');
    const { phone, code } = await c.req.json();

    if (!phone || !code) {
        throw new BadRequestException('Phone number and code are required');
    }

    //FOR TEST NUMBERS
    if (testNumbers.includes(phone) && testCodes.includes(code)) {
        // Check if user exists
        const { data: existingUser } = await supabase
            .from('users')
            .select('*')
            .eq('phone_number', phone)
            .single();

        // Generate tokens for existing user
        const token = await generateTokens(
            c,
            existingUser.id,
            existingUser.email || phone,
            existingUser.role_id,
        );

        return c.json({
            verified: true,
            first_time: false,
            access_token: token.accessToken,
            refresh_token: token.refreshToken,
            token_type: 'Bearer',
            expires_in: 3600
        });
    }



    // Verify the code
    const { data: verificationData, error: verificationError } = await supabase
        .from('verification_codes')
        .select('*')
        .eq('phone', phone)
        .eq('code', code)
        .gt('expires_at', new Date().toISOString())
        .single();

    if (verificationError || !verificationData) {
        throw new BadRequestException('Invalid or expired verification code');
    }

    // Check if user exists
    const { data: existingUser } = await supabase
        .from('users')
        .select('*')
        .eq('phone_number', phone)
        .single();

    // Delete used verification code
    await supabase
        .from('verification_codes')
        .delete()
        .eq('phone', phone);

    if (!existingUser) {
        return c.json({
            verified: true,
            first_time: true
        });
    }

    // Generate tokens for existing user
    const token = await generateTokens(
        c,
        existingUser.id,
        existingUser.email || phone,
        existingUser.role_id,
    );

    return c.json({
        verified: true,
        first_time: false,
        access_token: token.accessToken,
        refresh_token: token.refreshToken,
        token_type: 'Bearer',
        expires_in: 3600
    });
});

// Get current user (protected route)
authRoute.get('/me', authMiddleware, async (c: Context) => {
    const supabase = c.get('supabase')
    const user = c.get('user')

    const { data, error } = await supabase
        .from('users')
        .select('*, role:role_id(id, name)')
        .eq('id', user.id)
        .single();

    if (error || !data) {
        console.error('User fetch error:', error); // Debug log
        throw new HTTPException(401, { message: 'Unauthorized' });
    }

    return c.json({
        id: user.id,
        first_name: data?.first_name,
        last_name: data?.last_name,
        middle_name: data?.middle_name,
        role: { ...data?.role, permissions: [] },
        phone_number: data?.phone_number,
        country_code: data?.country_code,
        email: user.email,
        avatar_url: data?.avatar_url
    });
});

// Get users (protected route)
authRoute.get('/drivers', authMiddleware, async (c: Context) => {
    const supabase = c.get('supabase')
    const user = c.get('user')

    const { data, error } = await supabase
        .from('users')
        .select('*, role:role_id(id, name)')

    if (error || !data) {
        console.error('User fetch error:', error); // Debug log
        throw new HTTPException(401, { message: 'Unauthorized' });
    }

    return c.json(data.map((user: any) => ({
        id: user.id,
        first_name: user?.first_name,
        last_name: user?.last_name,
        middle_name: user?.middle_name,
        role: { ...user?.role, permissions: [] },
        phone_number: user?.phone_number,
        country_code: user?.country_code,
        email: user.email,
        avatar_url: user?.avatar_url
    })));
});

// Get users (protected route)
authRoute.get('/riders', authMiddleware, async (c: Context) => {
    const supabase = c.get('supabase')
    const user = c.get('user')

    const { data, error } = await supabase
        .from('users')
        .select('*, role:role_id(id, name)')

    if (error || !data) {
        console.error('User fetch error:', error); // Debug log
        throw new HTTPException(401, { message: 'Unauthorized' });
    }

    return c.json(data.map((user: any) => ({
        id: user.id,
        first_name: user?.first_name,
        last_name: user?.last_name,
        middle_name: user?.middle_name,
        role: { ...user?.role, permissions: [] },
        phone_number: user?.phone_number,
        country_code: user?.country_code,
        email: user.email,
        avatar_url: user?.avatar_url
    })));
});

// Refresh Token
authRoute.post('/refresh', refreshTokenMiddleware, async (c: Context) => c.json({ accessToken: c.get('accessToken') }));


export default authRoute
