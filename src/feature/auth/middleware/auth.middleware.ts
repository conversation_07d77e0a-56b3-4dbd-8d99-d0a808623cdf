import { Context, Next } from 'hono';
import { bearerAuth } from 'hono/bearer-auth';
import { verify } from 'hono/jwt';

// Authentication Middleware
const authMiddleware = async (c: Context, next: Next) => {
    return bearerAuth({
        invalidAuthenticationHeaderMessage: 'Invalid or missing authentication token',
        invalidTokenMessage : 'Invalid or expired token',
        noAuthenticationHeaderMessage : 'No authentication header provided',
        verifyToken: async (token, c) => {
            try {
                const decodedPayload = await verify(token, c.env.JWT_SECRET);

                if (!decodedPayload.sub || !decodedPayload.role) {
                    console.error('Token validation failed: Missing fields', decodedPayload);
                    return false;
                }

                c.set('user', {
                    id: decodedPayload.sub,
                    role: decodedPayload.role,
                    email: decodedPayload.email
                });

                return true;
            } catch (error) {
                console.error('Token verification failed:', error);
                return false;
            }
        },
    })(c, next);
};



export default authMiddleware;
