import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Context, Next } from 'hono';
import { bearerAuth } from 'hono/bearer-auth';
import { HTTPException } from 'hono/http-exception';
import { sign, verify } from 'hono/jwt';


// 🔄 Refresh Token Middleware
const refreshTokenMiddleware = async (c: Context, next: Next) => {
    return bearerAuth({
        invalidAuthenticationHeaderMessage : "Invalid Authentication Header",
        verifyToken: async (token, c) => {
            try {
                
                // Decode and validate the token
                const decodedPayload = await verify(token, c.env.JWT_REFRESH_SECRET);
                
                if (!decodedPayload.sub || !decodedPayload.role) {
                
                    throw new HTTPException(400, { message: 'Invalid token payload' });
                }

                // 🔍 Fetch user from Supabase by ID
                const supabase = c.get('supabase')
                
                const { data: user, error } = await supabase
                    .from('users')
                    .select('*, role:role_id(id, name)')
                    .eq('id', decodedPayload.sub)
                    .single();

                if (error || !user) {
                    console.error("❌ User not found or error:", error?.message);
                    throw new HTTPException(404, { message: 'User not found' });
                }

                // 🔥 Generate new access token
                const newAccessToken = await sign({
                    sub: user.id,
                    role: user.role,
                    orgId: user.organization_id,
                    email : decodedPayload.email,
                    exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour
                }, c.env.JWT_SECRET);

                // ✅ Store the new access token in context
                c.set('accessToken', newAccessToken);

                return true; // Token is valid
            } catch (error:any) {
                console.error("❌ Error during refresh token verification:", error.message);
                return false;
            }
        },
    })(c, next);
};

export default refreshTokenMiddleware;
