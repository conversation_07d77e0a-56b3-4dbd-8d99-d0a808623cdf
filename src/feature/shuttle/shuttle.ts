import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { ShuttleService } from './service/shuttle.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const shuttleRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
shuttleRoute.use('*', authMiddleware);

// Supabase middleware
shuttleRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Create a new shuttle (Admin and Partner only)
shuttleRoute.post('/', roleGuard([Role.ADMIN, Role.USER]), async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));
    const data = await c.req.json();

    // Validate required fields
    if (!data.name || !data.capacity || !data.registration_number) {
        throw new BadRequestException('Missing required fields: name, capacity, and registration_number are required');
    }

    // Validate data types
    if (typeof data.name !== 'string' ||
        typeof data.registration_number !== 'string' ||
        typeof data.capacity !== 'number' ||
        data.capacity <= 0) {
        throw new BadRequestException('Invalid data types or values: name and registration_number must be strings, capacity must be a positive number');
    }

    // If user is a partner, ensure they can only create shuttles for their organization
    const user = c.get('user');
    if (user.role === Role.USER) {
        data.partner_id = user.orgId;
    }

    const shuttle = await shuttleService.create(data);
    return c.json({
        status: 'success',
        data: shuttle,
        message: 'Shuttle created successfully'
    }, 201);
});

// Get all shuttles by partner
shuttleRoute.get('/by/partner/:partnerId', async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));

    const partnerId = c.req.param('partnerId');

    if (partnerId === undefined) {
        throw new BadRequestException('Partner ID is required');
    }

    const shuttles = await shuttleService.findAll(); //TODO!: MAKE THIS GET SHUTTLES BY PARTNER
    return c.json(shuttles);
});

shuttleRoute.get('/', async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));

    const shuttles = await shuttleService.findAll();
    return c.json(shuttles);
});

// Get a specific shuttle
shuttleRoute.get('/:id', roleGuard([Role.ADMIN.toString(), Role.PARTNER.toString()]), async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));
    const shuttle = await shuttleService.findOne(c.req.param('id'));

    // Check if partner has access to this shuttle
    const user = c.get('user');
    if (user.role === Role.USER && shuttle.partner_id !== user.orgId) {
        return c.json({ message: 'Shuttle not found' }, 404);
    }

    return c.json(shuttle);
});

// Update a shuttle
shuttleRoute.put('/:id', async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));
    const shuttleId = c.req.param('id');

    const data = await c.req.json();
    const shuttle = await shuttleService.update(shuttleId, data);
    return c.json(shuttle);
});

// Delete a shuttle
shuttleRoute.delete('/:id', roleGuard([Role.ADMIN.toString(), Role.USER.toString()]), async (c: Context) => {
    const shuttleService = new ShuttleService(c.get('supabase'));
    const shuttleId = c.req.param('id');

    // Check if partner has access to this shuttle
    const user = c.get('user');
    if (user.role === Role.USER) {
        const existingShuttle = await shuttleService.findOne(shuttleId);
        if (existingShuttle.partner_id !== user.orgId) {
            return c.json({ message: 'Shuttle not found' }, 404);
        }
    }

    await shuttleService.delete(shuttleId);
    return c.json({ message: 'Shuttle deleted successfully' }, 200);
});

export default shuttleRoute;