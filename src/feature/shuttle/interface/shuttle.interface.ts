export interface Shuttle {
    id: string;
    partner_id: string;
    name: string;
    model?: string;
    plate_number: string;
    capacity: number;
    amenities?: Record<string, any>;
    status: 'active' | 'inactive';
    created_at?: string;
    updated_at?: string;
}

export interface CreateShuttleDto {
    partner_id: string;
    name: string;
    model?: string;
    plate_number: string;
    capacity: number;
    amenities?: Record<string, any>;
}

export interface UpdateShuttleDto {
    name?: string;
    model?: string;
    plate_number?: string;
    capacity?: number;
    amenities?: Record<string, any>;
    status?: 'active' | 'inactive';
}