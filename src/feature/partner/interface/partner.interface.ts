export interface Partner {
    id: string;
    name: string;
    email: string;
    phone?: string;
    address?: string;
    commission_rate: number;
    status: 'active' | 'inactive';
    created_at?: string;
    updated_at?: string;
}

export interface CreatePartnerDto {
    name: string;
    email: string;
    phone?: string;
    address?: string;
    commission_rate: number;
}

export interface UpdatePartnerDto {
    name?: string;
    email?: string;
    phone?: string;
    address?: string;
    commission_rate?: number;
    status?: 'active' | 'inactive';
}