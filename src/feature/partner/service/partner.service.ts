import { SupabaseClient } from '@supabase/supabase-js';
import { Partner, CreatePartnerDto, UpdatePartnerDto } from '../interface/partner.interface';
import { BadRequestException } from '../../../core/exception';

export class PartnerService {
    constructor(private readonly supabase: SupabaseClient) {}

    async create(createPartnerDto: CreatePartnerDto): Promise<Partner> {
        const { data, error } = await this.supabase
            .from('partners')
            .insert([createPartnerDto])
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async findAll(): Promise<Partner[]> {
        const { data, error } = await this.supabase
            .from('partners')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data || [];
    }

    async findOne(id: string): Promise<Partner> {
        const { data, error } = await this.supabase
            .from('partners')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async update(id: string, updatePartnerDto: UpdatePartnerDto): Promise<Partner> {
        const { data, error } = await this.supabase
            .from('partners')
            .update(updatePartnerDto)
            .eq('id', id)
            .select()
            .single();

        if (error) {
            throw new BadRequestException(error.message);
        }

        return data;
    }

    async delete(id: string): Promise<void> {
        const { error } = await this.supabase
            .from('partners')
            .delete()
            .eq('id', id);

        if (error) {
            throw new BadRequestException(error.message);
        }
    }
}