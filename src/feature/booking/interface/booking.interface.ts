export interface Booking {
    id: string;
    user_id: string;
    schedule_id: string;
    number_of_tickets: number;
    total_amount: number;
    status: string;
    payment_ref?: string;
    payment_method?: string;
    booking_reference: string;
    created_at?: string;
    updated_at?: string;
    pickup_stop: number;
    drop_off_stop: number;
    booking_schedule_date: string;
}

export enum BookingStatus {
    PENDING = 'pending',
    CONFIRMED = 'confirmed',
    CANCELLED = 'cancelled',
    COMPLETED = 'completed'
}

export interface CreateBookingDto {
    schedule_id: string;
    number_of_tickets: number;
    total_amount: number;
    payment_ref?: string;
    payment_method?: string;
    pickup_stop: number;
    drop_off_stop: number;
    booking_schedule_date?: string;
}

export interface UpdateBookingDto {
    status?: BookingStatus;
    payment_ref?: string;
    payment_method?: string;
    number_of_tickets?: number;
    total_amount?: number;
    pickup_stop?: number;
    drop_off_stop?: number;
}