export interface PaystackInitializeRequest {
    email: string;
    amount: number; // amount in kobo (smallest currency unit)
    callback_url?: string;
    reference?: string;
    currency?: string;
    channels? : string[];
    metadata?: Record<string, any>;
}

export interface PaystackInitializeResponse {
    status: boolean;
    message: string;
    data: {
        authorization_url: string;
        access_code: string;
        reference: string;
    };
}

export interface PaystackVerifyResponse {
    status: boolean;
    message: string;
    data: {
        status: string;
        reference: string;
        amount: number;
        gateway_response: string;
        paid_at: string;
        created_at: string;
        channel: string;
        currency: string;
        customer: {
            email: string;
            customer_code: string;
        };
    };
}

export interface PaymentRecord {
    id: string;
    user_id: string;
    amount: number;
    currency: string;
    status: string;
    reference: string;
    gateway_response: string;
    payment_channel: string;
    created_at: string;
    updated_at: string;
}