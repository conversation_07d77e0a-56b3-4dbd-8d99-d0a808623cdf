import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { PaymentService } from './service/payment.service';
import { BadRequestException } from '../../core/exception';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const paymentRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
paymentRoute.use('*', authMiddleware);

// Supabase middleware
paymentRoute.use('*', async (c:Context, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Initialize payment
paymentRoute.post('/initialize', async (c: Context) => {
    const paymentService = new PaymentService(c.get('supabase'), c.env.PAYSTACK_SECRET_KEY);
    const data = await c.req.json();
    const user = c.get('user');

    // Validate required fields
    if (!data.amount || !data.email) {
        throw new BadRequestException('Amount and email are required');
    }

    // Add user_id to metadata
    data.metadata = {
        ...data.metadata,
        user_id: user.id
    };

    const result = await paymentService.initializeTransaction(data);
    return c.json(result.data);
});

// Verify payment
paymentRoute.get('/verify/:reference', async (c: Context) => {
    const paymentService = new PaymentService(c.get('supabase'), c.env.PAYSTACK_SECRET_KEY);
    const reference = c.req.param('reference');

    if (!reference) {
        throw new BadRequestException('Payment reference is required');
    }

    const result = await paymentService.verifyTransaction(reference);
    return c.json(result);
});

// Get payment by reference
paymentRoute.get('/:reference', roleGuard([Role.USER]), async (c: Context) => {
    const paymentService = new PaymentService(c.get('supabase'), c.env.PAYSTACK_SECRET_KEY);
    const reference = c.req.param('reference');

    if (!reference) {
        throw new BadRequestException('Payment reference is required');
    }

    const payment = await paymentService.getPaymentByReference(reference);
    return c.json(payment);
});

// Get user payments
paymentRoute.get('/', roleGuard([Role.USER]), async (c: Context) => {
    const paymentService = new PaymentService(c.get('supabase'), c.env.PAYSTACK_SECRET_KEY);
    const user = c.get('user');
    const payments = await paymentService.getUserPayments(user.id);
    return c.json(payments);
});

// Webhook
paymentRoute.get('/webhook', async (c: Context) => {
    return c.html('<h1></h1>');
});


export default paymentRoute;