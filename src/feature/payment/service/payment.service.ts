import { SupabaseClient } from '@supabase/supabase-js';
import { PaystackInitializeRequest, PaystackInitializeResponse, PaystackVerifyResponse, PaymentRecord } from '../interface/payment.interface';
import { BadRequestException } from '../../../core/exception';

export class PaymentService {
    private readonly PAYSTACK_SECRET_KEY: string;
    private readonly PAYSTACK_API_URL = 'https://api.paystack.co';

    constructor(
        private readonly supabase: SupabaseClient,
        private readonly secretKey: string
    ) {
        this.PAYSTACK_SECRET_KEY = secretKey;
    }

    async initializeTransaction(data: PaystackInitializeRequest): Promise<PaystackInitializeResponse> {
        try {

            // const total_amount = data.amount * 100;
            const total_amount = 1 * 100; //GHC1
            const payload = {
                ...data,
                amount: total_amount,
                callback_url : 'https://webhook.site/c4d38a45-b121-4986-91c4-64937995f7b2',
                channels: ['mobile_money']
            }

            const response = await fetch(`${this.PAYSTACK_API_URL}/transaction/initialize`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.PAYSTACK_SECRET_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new BadRequestException('Failed to initialize transaction');
            }

            const result: PaystackInitializeResponse = await response.json();

            // // Store the payment record in the database
            // await this.supabase
            //     .from('payments')
            //     .insert({
            //         reference: result.data.reference,
            //         amount: data.amount,
            //         currency: data.currency || 'NGN',
            //         status: 'pending',
            //         user_id: data.metadata?.user_id,
            //         created_at: new Date().toISOString(),
            //         updated_at: new Date().toISOString()
            //     });

            return result;
        } catch (error: any) {
            throw new BadRequestException(error.message || 'Payment initialization failed');
        }
    }

    async verifyTransaction(reference: string): Promise<PaystackVerifyResponse> {
        try {
            const response = await fetch(`${this.PAYSTACK_API_URL}/transaction/verify/${reference}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.PAYSTACK_SECRET_KEY}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new BadRequestException('Failed to verify transaction');
            }

            const result: PaystackVerifyResponse = await response.json();

            // // Update the payment record in the database
            // await this.supabase
            //     .from('payments')
            //     .update({
            //         status: result.data.status,
            //         gateway_response: result.data.gateway_response,
            //         payment_channel: result.data.channel,
            //         updated_at: new Date().toISOString()
            //     })
            //     .eq('reference', reference);

            return result;
        } catch (error: any) {
            throw new BadRequestException(error.message || 'Payment verification failed');
        }
    }

    async getPaymentByReference(reference: string): Promise<PaymentRecord | null> {
        const { data, error } = await this.supabase
            .from('payments')
            .select('*')
            .eq('reference', reference)
            .single();

        if (error) {
            throw new BadRequestException('Failed to fetch payment record');
        }

        return data;
    }

    async getUserPayments(userId: string): Promise<PaymentRecord[]> {
        const { data, error } = await this.supabase
            .from('payments')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        if (error) {
            throw new BadRequestException('Failed to fetch user payments');
        }

        return data || [];
    }
}