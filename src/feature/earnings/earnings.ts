import { Context, Hono } from 'hono';
import { Bindings } from '../../core/type/bindings';
import { EarningsService } from './service/earnings.service';
import authMiddleware from '../../feature/auth/middleware/auth.middleware';
import { roleGuard } from '../../core/middleware/roleGuard.middleware';
import { Role } from '../../core/enum/roleGuard.enum';
import { BadRequestException } from '../../core/exception';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Initialize Supabase client in Hono context
declare module 'hono' {
    interface ContextVariableMap {
        supabase: SupabaseClient
    }
}

const earningsRoute = new Hono<{ Bindings: Bindings }>();

// Apply authentication middleware to all routes
earningsRoute.use('*', authMiddleware);

// Supabase middleware
earningsRoute.use('*', async (c, next) => {
    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY)
    c.set('supabase', supabase)
    await next()
})

// Get partner total earnings
earningsRoute.get('/partner/:id', async (c: Context) => {
    const earningsService = new EarningsService(c.get('supabase'));
    const user = c.get('user');
    
    // Extract query parameters
    const filters = {
        start_date: c.req.query('start_date'),
        end_date: c.req.query('end_date'),
        partner_id: c.req.param('id')
    };
    
    try {
        const earnings = await earningsService.getTotalEarnings(filters);
        return c.json(earnings);
    } catch (error) {
        if (error instanceof BadRequestException) {
            return c.json({
                status: 'error',
                message: error.message
            }, 400);
        }
        throw error;
    }
});

export default earningsRoute;
