export interface Route {
    id: string;
    partner_id: string;
    name: string;
    origin: {
        latitude: number;
        longitude: number;
        address: string;
    };
    destination: {
        latitude: number;
        longitude: number;
        address: string;
    };
    stops?: {
        latitude: number;
        longitude: number;
        address: string;
        sequence: number;
    }[];
    distance?: number;
    estimated_duration?: string;
    base_price: number;
    status: 'active' | 'inactive';
    created_at?: string;
    updated_at?: string;
}

export interface CreateRouteDto {
    partner_id: string;
    name: string;
    origin_location: {
        latitude: number;
        longitude: number;
        address: string;
    };
    destination_location: {
        latitude: number;
        longitude: number;
        address: string;
    };
    stops?: {
        latitude: number;
        longitude: number;
        address: string;
        sequence: number;
    }[];
    distance?: number;
    estimated_duration?: string;
    base_price: number;
}

export interface UpdateRouteDto {
    name?: string;
    origin_location?: {
        latitude: number;
        longitude: number;
        address: string;
    };
    destination_location?: {
        latitude: number;
        longitude: number;
        address: string;
    };
    stops?: {
        latitude: number;
        longitude: number;
        address: string;
        sequence: number;
    }[];
    distance?: number;
    estimated_duration?: string;
    base_price?: number;
    status?: 'active' | 'inactive';
}